import base64
import binascii

# من الـ MPD الذي حصلنا عليه سابقاً
widevine_pssh_b64 = "AAAAT3Bzc2gBAAAA7e+LqXnWSs6jyCfc1R0h7QAAAAHYiex8tc2Ky0T3AnJqVq4hAAAAGyITRVZNQkNBQ1RJT05IRFkyMDIzTUjj3JWbBg=="

print("=== Widevine PSSH Analysis ===")
print(f"Base64 PSSH: {widevine_pssh_b64}")
print()

# فك تشفير Base64
pssh_bytes = base64.b64decode(widevine_pssh_b64)
print(f"PSSH Length: {len(pssh_bytes)} bytes")
print(f"PSSH Hex: {binascii.hexlify(pssh_bytes).decode().upper()}")
print()

# تحليل PSSH Box Structure
print("=== PSSH Box Structure ===")

# PSSH Box Size (first 4 bytes)
box_size = int.from_bytes(pssh_bytes[0:4], 'big')
print(f"Box Size: {box_size} bytes (0x{box_size:08X})")

# PSSH Box Type (next 4 bytes) - should be 'pssh'
box_type = pssh_bytes[4:8].decode('ascii')
print(f"Box Type: {box_type}")

# Version (1 byte)
version = pssh_bytes[8]
print(f"Version: {version}")

# Flags (3 bytes)
flags = pssh_bytes[9:12]
print(f"Flags: {binascii.hexlify(flags).decode().upper()}")

# System ID (16 bytes) - Widevine UUID
system_id = pssh_bytes[12:28]
widevine_uuid = "EDEF8BA9-79D6-4ACE-A3C8-27DCD51D21ED"
print(f"System ID: {binascii.hexlify(system_id).decode().upper()}")
print(f"Expected Widevine UUID: {widevine_uuid.replace('-', '')}")

# Data Size (4 bytes)
data_size = int.from_bytes(pssh_bytes[28:32], 'big')
print(f"Data Size: {data_size} bytes (0x{data_size:08X})")

# PSSH Data (remaining bytes)
pssh_data = pssh_bytes[32:32+data_size]
print(f"PSSH Data: {binascii.hexlify(pssh_data).decode().upper()}")
print()

print("=== Complete PSSH Box (for tools) ===")
print(f"Hex: {binascii.hexlify(pssh_bytes).decode().upper()}")
print()

# استخراج Key ID من الـ PSSH data
print("=== Key ID Extraction ===")
if len(pssh_data) >= 16:
    # في Widevine، الـ Key ID عادة يكون في بداية الـ data
    # لكن هنا نحتاج لتحليل الـ protobuf structure
    print("PSSH Data contains protobuf structure")
    
    # البحث عن الـ Key ID pattern
    # Key ID من الـ PlayReady: d889ec7c-b5cd-8acb-44f7-02726a56ae21
    expected_kid_hex = "D889EC7CB5CD8ACB44F702726A56AE21"
    
    if expected_kid_hex.lower() in binascii.hexlify(pssh_data).decode().upper():
        print(f"✓ Key ID found in PSSH data: {expected_kid_hex}")
    else:
        print("Key ID not found in expected format")
        print("Raw PSSH data for manual analysis:")
        for i in range(0, len(pssh_data), 16):
            chunk = pssh_data[i:i+16]
            hex_chunk = binascii.hexlify(chunk).decode().upper()
            ascii_chunk = ''.join([chr(b) if 32 <= b <= 126 else '.' for b in chunk])
            print(f"  {i:04X}: {hex_chunk:<32} {ascii_chunk}")

print()
print("=== Usage Instructions ===")
print("1. Use this PSSH with tools like:")
print("   - yt-dlp with --allow-unplayable-formats")
print("   - N_m3u8DL-RE")
print("   - Decryption tools")
print()
print("2. PSSH for command line tools:")
print(f"   --pssh {widevine_pssh_b64}")
print()
print("3. Key ID for license requests:")
print("   d889ec7c-b5cd-8acb-44f7-02726a56ae21")
