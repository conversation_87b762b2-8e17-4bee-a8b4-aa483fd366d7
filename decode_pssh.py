import base64
import xml.etree.ElementTree as ET

# PlayReady PSSH data from the MPD
pssh_data = "AAADvHBzc2gBAAAAmgTweZhAQoarkuZb4IhflQAAAAHYiex8tc2Ky0T3AnJqVq4hAAADiIgDAAABAAEAfgM8AFcAUgBNAEgARQBBAEQARQBSACAAeABtAGwAbgBzAD0AIgBoAHQAdABwADoALwAvAHMAYwBoAGUAbQBhAHMALgBtAGkAYwByAG8AcwBvAGYAdAAuAGMAbwBtAC8ARABSAE0ALwAyADAAMAA3AC8AMAAzAC8AUABsAGEAeQBSAGUAYQBkAHkASABlAGEAZABlAHIAIgAgAHYAZQByAHMAaQBvAG4APQAiADQALgAwAC4AMAAuADAAIgA+ADwARABBAFQAQQA+ADwAUABSAE8AVABFAEMAVABJAE4ARgBPAD4APABLAEUAWQBMAEUATgA+ADEANgA8AC8ASwBFAFkATABFAE4APgA8AEEATABHAEkARAA+AEEARQBTAEMAVABSADwALwBBAEwARwBJAEQAPgA8AC8AUABSAE8AVABFAEMAVABJAE4ARgBPAD4APABLAEkARAA+AGYATwB5AEoAMgBNADIAMQB5ADQAcABFADkAdwBKAHkAYQBsAGEAdQBJAFEAPQA9ADwALwBLAEkARAA+ADwATABBAF8AVQBSAEwAPgBoAHQAdABwAHMAOgAvAC8AcABsAGEAeQByAGUAYQBkAHkALQBsAGkAYwBlAG4AcwBlAC4AZAByAG0ALgB0AGUAYwBoAG4AbwBsAG8AZwB5AC8AcgBpAGcAaAB0AHMAbQBhAG4AYQBnAGUAcgAuAGEAcwBtAHgAPAAvAEwAQQBfAFUAUgBMAD4APABMAFUASQBfAFUAUgBMAD4AaAB0AHQAcABzADoALwAvAHAAbABhAHkAcgBlAGEAZAB5AC0AbABpAGMAZQBuAHMAZQAuAGQAcgBtAC4AdABlAGMAaABuAG8AbABvAGcAeQAvAHIAaQBnAGgAdABzAG0AYQBuAGEAZwBlAHIALgBhAHMAbQB4ADwALwBMAFUASQBfAFUAUgBMAD4APABEAFMAXwBJAEQAPgBnAHcASQBDAEkAOAB5AGYASQBVAEcAZgA0AFIALwA1AHEATwBXAHUAcQBnAD0APQA8AC8ARABTAF8ASQBEAD4APABDAEgARQBDAEsAUwBVAE0APgBnAFEAMQBsAFMAUQA4AGEAUwBOAHMAPQA8AC8AQwBIAEUAQwBLAFMAVQBNAD4APAAvAEQAQQBUAEEAPgA8AC8AVwBSAE0ASABFAEEARABFAFIAPgA="

try:
    # Decode the base64 PSSH data
    decoded_data = base64.b64decode(pssh_data)
    
    # Skip the PSSH header (first 32 bytes typically)
    # Look for the XML data which starts after the header
    xml_start = decoded_data.find(b'<WRMHEADER')
    if xml_start != -1:
        xml_data = decoded_data[xml_start:]
        
        # Convert from UTF-16 to UTF-8 (<PERSON><PERSON><PERSON><PERSON> uses UTF-16)
        xml_string = xml_data.decode('utf-16le', errors='ignore')
        
        # Clean up the XML string
        xml_string = xml_string.split('\x00')[0]  # Remove null bytes
        
        print("Decoded PlayReady Header:")
        print(xml_string)
        
        # Parse the XML to extract license URLs
        try:
            root = ET.fromstring(xml_string)
            
            # Look for LA_URL (License Acquisition URL)
            la_url = root.find('.//LA_URL')
            if la_url is not None:
                print(f"\nPlayReady License Server URL: {la_url.text}")
            
            # Look for LUI_URL (License Update Interface URL)
            lui_url = root.find('.//LUI_URL')
            if lui_url is not None:
                print(f"PlayReady LUI URL: {lui_url.text}")
                
        except ET.ParseError as e:
            print(f"Error parsing XML: {e}")
            
    else:
        print("Could not find XML data in PSSH")
        
except Exception as e:
    print(f"Error decoding PSSH: {e}")

# Also check for Widevine license server (though it's not typically in the PSSH for Widevine)
print("\n" + "="*50)
print("Note: For Widevine DRM, the license server URL is typically not embedded in the PSSH.")
print("It's usually provided separately by the content provider or streaming service.")
print("Common Widevine license server patterns include:")
print("- https://[domain]/widevine/license")
print("- https://[domain]/drm/widevine")
print("- https://[domain]/license/widevine")
